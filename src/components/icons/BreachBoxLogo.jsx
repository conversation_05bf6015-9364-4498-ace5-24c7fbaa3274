import React from 'react';

const BreachBoxLogo = ({ 
  width = 200, 
  height = 60, 
  className = "",
  showText = true,
  variant = "default" // default, minimal, icon-only
}) => {
  const primaryColor = "#9fef00";
  const secondaryColor = "#00ff41";
  const darkColor = "#1a1a1a";

  if (variant === "icon-only") {
    return (
      <svg 
        width={width} 
        height={height} 
        viewBox="0 0 60 60" 
        className={className}
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Cyber Box Icon */}
        <defs>
          <linearGradient id="breachGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={primaryColor} />
            <stop offset="100%" stopColor={secondaryColor} />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>
        
        {/* Main Box */}
        <rect 
          x="8" y="8" 
          width="44" height="44" 
          rx="8" 
          fill="none" 
          stroke="url(#breachGradient)" 
          strokeWidth="2"
          filter="url(#glow)"
        />
        
        {/* Inner Circuit Pattern */}
        <g stroke={primaryColor} strokeWidth="1.5" fill="none">
          <path d="M15 20 L25 20 L25 30 L35 30" />
          <path d="M45 25 L35 25 L35 35 L25 35" />
          <circle cx="25" cy="20" r="2" fill={primaryColor} />
          <circle cx="35" cy="30" r="2" fill={secondaryColor} />
          <circle cx="25" cy="35" r="2" fill={primaryColor} />
        </g>
        
        {/* Lock Symbol */}
        <g transform="translate(20, 15)">
          <rect x="5" y="10" width="10" height="8" rx="1" fill={primaryColor} opacity="0.8"/>
          <path d="M7 10 L7 7 Q7 5 10 5 Q13 5 13 7 L13 10" 
                stroke={primaryColor} strokeWidth="1.5" fill="none"/>
        </g>
      </svg>
    );
  }

  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 300 80" 
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="breachTextGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor={primaryColor} />
          <stop offset="50%" stopColor={secondaryColor} />
          <stop offset="100%" stopColor={primaryColor} />
        </linearGradient>
        <filter id="textGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      {/* Icon */}
      <g transform="translate(10, 10)">
        {/* Main Box */}
        <rect 
          x="0" y="0" 
          width="60" height="60" 
          rx="12" 
          fill="none" 
          stroke="url(#breachTextGradient)" 
          strokeWidth="3"
          filter="url(#textGlow)"
        />
        
        {/* Circuit Pattern */}
        <g stroke={primaryColor} strokeWidth="2" fill="none">
          <path d="M12 25 L30 25 L30 40 L48 40" />
          <path d="M48 30 L30 30 L30 45 L12 45" />
          <circle cx="30" cy="25" r="3" fill={primaryColor} />
          <circle cx="48" cy="40" r="3" fill={secondaryColor} />
          <circle cx="12" cy="45" r="3" fill={primaryColor} />
        </g>
        
        {/* Breach Symbol */}
        <g transform="translate(20, 15)">
          <rect x="5" y="15" width="15" height="12" rx="2" fill={primaryColor} opacity="0.9"/>
          <path d="M8 15 L8 10 Q8 7 15 7 Q22 7 22 10 L22 15" 
                stroke={primaryColor} strokeWidth="2" fill="none"/>
          {/* Crack/Breach line */}
          <path d="M15 20 L25 10" stroke={secondaryColor} strokeWidth="2"/>
        </g>
      </g>
      
      {/* Text */}
      {showText && (
        <g>
          <text 
            x="90" 
            y="35" 
            fontFamily="'Orbitron', 'Courier New', monospace" 
            fontSize="24" 
            fontWeight="bold"
            fill="url(#breachTextGradient)"
            filter="url(#textGlow)"
          >
            BREACH
          </text>
          <text 
            x="90" 
            y="60" 
            fontFamily="'Orbitron', 'Courier New', monospace" 
            fontSize="24" 
            fontWeight="bold"
            fill="url(#breachTextGradient)"
            filter="url(#textGlow)"
          >
            BOX
          </text>
          
          {/* Tagline */}
          <text 
            x="220" 
            y="45" 
            fontFamily="'Roboto Mono', monospace" 
            fontSize="10" 
            fill={primaryColor}
            opacity="0.8"
          >
            CYBER SECURITY TRAINING
          </text>
        </g>
      )}
      
      {/* Animated elements */}
      <g>
        <circle cx="250" cy="20" r="2" fill={secondaryColor}>
          <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="260" cy="25" r="1.5" fill={primaryColor}>
          <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
        </circle>
        <circle cx="270" cy="30" r="1" fill={secondaryColor}>
          <animate attributeName="opacity" values="0.5;1;0.5" dur="1.8s" repeatCount="indefinite"/>
        </circle>
      </g>
    </svg>
  );
};

export default BreachBoxLogo;
