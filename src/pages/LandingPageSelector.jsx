import React, { useState } from 'react';
import { motion } from 'framer-motion';
import BreachBoxLanding from './BreachBoxLanding';
import CyberForceLanding from './CyberForceLanding';
import XCerberusLanding from './XCerberusLanding';
import ZilalLinuxLanding from './ZilalLinuxLanding';

const LandingPageSelector = () => {
  const [selectedLanding, setSelectedLanding] = useState('BreachBox');

  const landingOptions = [
    {
      id: 'BreachBox',
      name: 'BreachBox Landing',
      description: 'TryHackMe/HackTheBox inspired green theme with modern animations',
      color: '#9fef00',
      component: <BreachBoxLanding />
    },
    {
      id: 'CyberForce',
      name: 'CyberForce Landing',
      description: 'Original blue cyberpunk theme with comprehensive features',
      color: '#3B82F6',
      component: <CyberForceLanding />
    },
    {
      id: 'XCerberus',
      name: 'XCerberus Landing',
      description: 'Professional corporate design with business focus',
      color: '#88cc14',
      component: <XCerberusLanding />
    },
    {
      id: 'ZilalLinux',
      name: 'Z<PERSON>lLinux Landing',
      description: 'Linux-focused design with terminal aesthetics',
      color: '#10B981',
      component: <ZilalLinuxLanding />
    }
  ];

  const selectedOption = landingOptions.find(option => option.id === selectedLanding);

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Landing Page Selector Header */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-gray-800/95 backdrop-blur-sm border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">
                🎨 Landing Page Selector
              </h1>
              <p className="text-gray-300 text-sm">
                Choose your preferred landing page design
              </p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {landingOptions.map((option) => (
                <motion.button
                  key={option.id}
                  onClick={() => setSelectedLanding(option.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    selectedLanding === option.id
                      ? 'text-black shadow-lg transform scale-105'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                  style={{
                    backgroundColor: selectedLanding === option.id ? option.color : undefined
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {option.name}
                </motion.button>
              ))}
            </div>
          </div>
          
          {/* Selected Option Description */}
          <div className="mt-4 p-3 bg-gray-700/50 rounded-lg">
            <p className="text-gray-300 text-sm">
              <span className="font-semibold text-white">Current: </span>
              {selectedOption?.description}
            </p>
          </div>
        </div>
      </div>

      {/* Landing Page Content */}
      <div className="pt-32">
        <motion.div
          key={selectedLanding}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {selectedOption?.component}
        </motion.div>
      </div>

      {/* Quick Actions Footer */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-gray-800/90 backdrop-blur-sm rounded-lg p-4 border border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 rounded-full animate-pulse" style={{ backgroundColor: selectedOption?.color }}></div>
            <span className="text-white text-sm font-medium">
              {selectedOption?.name}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPageSelector;
