import { lazy, useEffect } from 'react';
import performanceService from './services/PerformanceService';
import seoService from './services/SEOService';
import errorMonitoring from './utils/errorMonitoring';
import { initializeSafeErrorHandling } from './utils/safeErrorHandling';
import dbOptimizer from './services/DatabaseOptimizer';
import moduleLoader from './services/ModuleLoader';
import fallbackApiService from './services/FallbackApiService';
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import SmoothScroll from './components/common/SmoothScroll';
import ScrollToTop from './components/common/ScrollToTop';
import { Toaster } from 'react-hot-toast';
import './utils/testDatabaseConnection';
import './styles/clean-network-learning.css';
import './styles/module-content-fix.css';
import './styles/module-content-fix.css';
import { SubscriptionProvider } from './contexts/SubscriptionContext';
import { SimpleSubscriptionProvider } from './contexts/SimpleSubscriptionContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { GlobalThemeProvider } from './contexts/GlobalThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { FallbackChallengeProvider } from './contexts/FallbackChallengeContext';
import { LeaderboardProvider } from './contexts/LeaderboardContext';
import { LearningProvider } from './contexts/LearningContext';
import { LearningPreferencesProvider } from './contexts/LearningPreferencesContext';
import { LanguageProvider } from './contexts/LanguageContext';
// StoreProvider removed - no store functionality
import { AnalyticsProvider } from './contexts/AnalyticsContext';
import { GuestProgressProvider } from './contexts/GuestProgressContext';
import { UnifiedDashboardProvider } from './contexts/UnifiedDashboardContext';
import { XPProvider } from './contexts/XPContext';
// import SecurityInsightsMiddleware from './middleware/SecurityInsightsMiddleware';

// Gamification Pages
import AchievementsPage from './pages/gamification/AchievementsPage';
import DailyChallengesPage from './pages/gamification/DailyChallengesPage';
import LeaderboardPage from './pages/gamification/LeaderboardPage';
import AdminGamificationDashboard from './pages/admin/AdminGamificationDashboard';
import GamificationAnalytics from './pages/admin/GamificationAnalytics';
import NotificationSystem from './components/gamification/NotificationSystem';
import { LearningModuleProvider } from './contexts/LearningModuleContext';
import EnhancedProfilePage from './pages/EnhancedProfilePage';
import SimpleErrorBoundary from './components/common/SimpleErrorBoundary';
import ContextErrorBoundary from './components/common/ContextErrorBoundary';
import SystemStatus from './components/common/SystemStatus';
import GlobalHeader from './components/ui/GlobalHeader';
import CyberForceFooter from './components/ui/CyberForceFooter';
import LazyLoadWrapper from './components/LazyLoadWrapper';
import ProtectedRoute from './components/ProtectedRoute';
import DashboardRouteHandler from './components/DashboardRouteHandler';
import DashboardErrorBoundary from './components/dashboard/DashboardErrorBoundary';
import GridOverlay from './components/GridOverlay';
import PerformanceMonitor from './components/PerformanceMonitor';
import Test from './test';

// Eagerly loaded components
import LearningModulePage from './pages/LearningModulePage';


// Lazy loaded pages
const StartHack = lazy(() => import('./pages/StartHack'));
const Challenger = lazy(() => import('./pages/Challenger'));
const Products = lazy(() => import('./pages/Products'));
// Main dashboard component - using proper dashboard layout
const DashboardLayout = lazy(() => import('./components/dashboard/DashboardLayout'));
const DashboardHome = lazy(() => import('./components/dashboard/DashboardHome'));
const UnifiedDashboard = lazy(() => import('./components/dashboard/UnifiedDashboard'));
const ChallengesHub = lazy(() => import('./components/dashboard/ChallengesHub'));
const ChallengeDetail = lazy(() => import('./components/dashboard/ChallengeDetail'));
const SecurityInsights = lazy(() => import('./components/dashboard/SecurityInsights'));
const UserProfile = lazy(() => import('./components/dashboard/UserProfile'));
const SimpleProfileTest = lazy(() => import('./components/dashboard/SimpleProfileTest'));
const ProfileDebug = lazy(() => import('./components/dashboard/ProfileDebug'));
const MinimalProfile = lazy(() => import('./components/dashboard/MinimalProfile'));
const EnhancedUserProfile = lazy(() => import('./components/dashboard/EnhancedUserProfile'));
const SimpleUserProfile = lazy(() => import('./components/dashboard/SimpleUserProfile'));

// Import the fixed dashboard components
const Achievements = lazy(() => import('./components/dashboard/QuickFixes').then(module => ({ default: module.Achievements })));
const DailyChallenges = lazy(() => import('./components/dashboard/QuickFixes').then(module => ({ default: module.DailyChallenges })));
const Leaderboards = lazy(() => import('./components/dashboard/QuickFixes').then(module => ({ default: module.Leaderboards })));
const Certifications = lazy(() => import('./components/dashboard/QuickFixes').then(module => ({ default: module.Certifications })));
const Progress = lazy(() => import('./components/dashboard/QuickFixes').then(module => ({ default: module.Progress })));

// Learning Path Components
const LearningPathDetail = lazy(() => import('./components/dashboard/LearningPathDetail'));
const ModuleContent = lazy(() => import('./components/dashboard/ModuleContent'));
const RichModuleDetail = lazy(() => import('./components/learning/RichModuleDetail'));
const LearningPathsSimple = lazy(() => import('./components/dashboard/LearningPathsSimple'));
const DatabaseTest = lazy(() => import('./components/debug/DatabaseTest'));
const AuthDebug = lazy(() => import('./components/debug/AuthDebug'));
const DatabaseDebugComponent = lazy(() => import('./components/debug/DatabaseDebugComponent'));
const OSContentTest = lazy(() => import('./components/debug/OSContentTest'));
const SimpleOSTest = lazy(() => import('./components/debug/SimpleOSTest'));
const DirectOSTest = lazy(() => import('./components/debug/DirectOSTest'));
const NavbarTest = lazy(() => import('./components/debug/NavbarTest'));
const Simulations = lazy(() => import('./pages/Simulations'));
const OffensiveSimulations = lazy(() => import('./pages/OffensiveSimulations'));
const DefensiveSimulations = lazy(() => import('./pages/DefensiveSimulations'));
const PageSecurityInsights = lazy(() => import('./pages/SecurityInsights'));
const EnhancedSecurityInsights = lazy(() => import('./pages/EnhancedSecurityInsights'));
const About = lazy(() => import('./pages/About'));
const Services = lazy(() => import('./pages/Services'));
const TestPage = lazy(() => import('./pages/TestPage'));
const CyberForceLanding = lazy(() => import('./pages/CyberForceLanding'));
const BreachBoxLanding = lazy(() => import('./pages/BreachBoxLanding'));
const Blog = lazy(() => import('./pages/Blog'));

// New Learning Paths Pages
const LearningPaths = lazy(() => import('./pages/LearningPaths'));
const ModuleDetail = lazy(() => import('./pages/ModuleDetail'));

// Personalized Learning Components
const LearningPreferences = lazy(() => import('./components/learning/LearningPreferences'));
const SkillAssessment = lazy(() => import('./components/learning/SkillAssessment'));
const SkillMapping = lazy(() => import('./components/learning/SkillMapping'));
const TimeTrackingDashboard = lazy(() => import('./components/learning/TimeTrackingDashboard'));
const VirtualLab = lazy(() => import('./components/learning/VirtualLab'));
const PathWelcome = lazy(() => import('./components/learning/PathWelcome'));

const SimpleLogin = lazy(() => import('./pages/SimpleLogin'));
const PremiumLogin = lazy(() => import('./pages/PremiumLogin'));
const SimpleSignup = lazy(() => import('./pages/SimpleSignup'));
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/ResetPassword'));
const VerifyEmail = lazy(() => import('./pages/VerifyEmail'));
const AuthCallback = lazy(() => import('./components/auth/AuthCallback'));
// SimpleDashboard removed - using only EnhancedDashboard
const SimplePricing = lazy(() => import('./pages/SimplePricing'));
const SimpleLearnModules = lazy(() => import('./pages/SimpleLearnModules'));
const StaticChallenges = lazy(() => import('./pages/StaticChallenges'));
const StaticLearning = lazy(() => import('./pages/StaticLearning'));
const ModulePreview = lazy(() => import('./pages/ModulePreview'));
const TestLogin = lazy(() => import('./pages/TestLogin'));
const ChallengeSimulator = lazy(() => import('./components/challenges/ChallengeSimulator'));
const SimpleLeaderboard = lazy(() => import('./pages/SimpleLeaderboard'));
const SimpleContact = lazy(() => import('./pages/SimpleContact'));
const TermsAndConditions = lazy(() => import('./pages/TermsAndConditions'));
const PrivacyPolicy = lazy(() => import('./pages/PrivacyPolicy'));

// Premium content pages
const PremiumPrograms = lazy(() => import('./pages/premium/ProgramsPage'));
const LearnMorePage = lazy(() => import('./pages/premium/LearnMorePage'));
const FeaturesPage = lazy(() => import('./pages/premium/FeaturesPage'));
const EnhancedAdminDashboard = lazy(() => import('./pages/admin/EnhancedAdminDashboard'));
const ModernAdminDashboard = lazy(() => import('./pages/admin/ModernAdminDashboard'));
const ComprehensiveSuperAdminDashboard = lazy(() => import('./components/admin/ComprehensiveSuperAdminDashboard'));
const EnhancedSuperAdminDashboard = lazy(() => import('./components/admin/EnhancedSuperAdminDashboard'));
const DatabaseTestPanel = lazy(() => import('./components/admin/DatabaseTestPanel'));
// Team components removed - no business tier
const SubscriptionManagement = lazy(() => import('./pages/SubscriptionManagement'));
const SubscriptionDebug = lazy(() => import('./pages/SubscriptionDebug'));

// New global pages with role-based access
const GlobalLearn = lazy(() => import('./pages/GlobalLearn'));
const GlobalChallenges = lazy(() => import('./pages/GlobalChallenges'));
const GlobalStartHack = lazy(() => import('./pages/GlobalStartHack'));

// Wrapper component to use the theme context
const GlobalHeaderWrapper = () => {
  return <GlobalHeader />;
};

const GlobalFooterWrapper = () => {
  return <CyberForceFooter />;
};



function App() {
  // Initialize performance monitoring and SEO optimization
  useEffect(() => {
    try {
      // 🛡️ CRITICAL: Initialize safe error handling first to prevent infinite loops
      initializeSafeErrorHandling();

      // Initialize performance monitoring
      performanceService.initializeMonitoring();

      // Initialize SEO optimization
      seoService.optimizeForCoreWebVitals();
      seoService.updatePageSEO('home');

      // Initialize error monitoring (only in production)
      if (import.meta.env.MODE === 'production') {
        errorMonitoring.initialize();
      }

      // Initialize fallback API service to suppress 404 errors
      fallbackApiService.initialize();

      // Initialize performance optimizer
      performanceOptimizer.initialize();

      // Initialize production-ready services
      // Preload critical data for better performance
      const initProductionServices = async () => {
        try {
          // Initialize module loader - TEMPORARILY DISABLED TO TEST FIXES
          // await moduleLoader.preloadCriticalModules('networking-fundamentals');
          // await moduleLoader.preloadCriticalModules('operating-systems');

          // Clear any stale caches
          dbOptimizer.clearCache();
        } catch (error) {
          // Some production services failed to initialize
        }
      };

      // Initialize demo mode error suppression
      const initDemoMode = async () => {
        const { suppressDemoErrors } = await import('./utils/demoSupabaseWrapper');
        suppressDemoErrors();
      };

      // SecurityInsightsMiddleware initialization moved here
      const initSecurityInsights = async () => {
        const SecurityInsightsMiddleware = (await import('./middleware/SecurityInsightsMiddleware')).default;
        await SecurityInsightsMiddleware.initialize();
      };

      // Initialize production services first
      initProductionServices();

      // Initialize demo mode
      initDemoMode().catch(error => {
        // Demo mode initialization skipped
      });

      // Then initialize security insights
      initSecurityInsights().catch(error => {
        // Security Insights Middleware initialization skipped
      });
    } catch (error) {
      // Initialization skipped (CORS prevention)
    }
  }, []);

  return (
    <SimpleErrorBoundary>
      <GlobalThemeProvider>
        <LanguageProvider>
          <AuthProvider>
            <ThemeProvider>
              <SimpleSubscriptionProvider>
                <SubscriptionProvider>
                  <ContextErrorBoundary>
                    <UnifiedDashboardProvider>
                      <ContextErrorBoundary contextName="Challenge">
                        <FallbackChallengeProvider>
                          <ContextErrorBoundary contextName="Leaderboard">
                            <LeaderboardProvider>
                              <ContextErrorBoundary contextName="Learning">
                                <LearningProvider>
                                  <LearningPreferencesProvider>
                                    <ContextErrorBoundary contextName="LearningModule">
                                      <LearningModuleProvider>
                                {/* StoreProvider removed */}
                                  <AnalyticsProvider>
                                    <GuestProgressProvider>
                                      <XPProvider>
                                          <Router
                                            future={{
                                              v7_startTransition: true,
                                              v7_relativeSplatPath: true
                                            }}
                                          >
                                            <SmoothScroll>
                                              <div className="min-h-screen bg-white text-gray-900 relative overflow-hidden">
                                                {/* Simple Grid Background */}
                                                <GridOverlay />
                                                <PerformanceMonitor />

                                                {/* Main Content */}
                                                <SimpleErrorBoundary componentName="MainContent">
                                                  <div className="relative z-50">
                                                    {/* Use GlobalHeader with theme toggle */}
                                                    <Routes>
                                                      <Route path="/dashboard/*" element={null} />
                                                      {/* simplified-dashboard redirects to /dashboard */}
                                                      <Route path="/enhanced-dashboard/*" element={null} />
                                                      <Route path="*" element={<GlobalHeaderWrapper />} />
                                                    </Routes>

                                                    <Routes>
                                                      <Route path="/" element={<LazyLoadWrapper><BreachBoxLanding /></LazyLoadWrapper>} />
                                                      <Route path="/login" element={<LazyLoadWrapper><SimpleLogin /></LazyLoadWrapper>} />
                                                      <Route path="/premium-login" element={<LazyLoadWrapper><PremiumLogin /></LazyLoadWrapper>} />
                                                      <Route path="/signup" element={<LazyLoadWrapper><SimpleSignup /></LazyLoadWrapper>} />
                                                      <Route path="/forgot-password" element={<LazyLoadWrapper><ForgotPassword /></LazyLoadWrapper>} />
                                                      <Route path="/reset-password" element={<LazyLoadWrapper><ResetPassword /></LazyLoadWrapper>} />
                                                      <Route path="/verify-email" element={<LazyLoadWrapper><VerifyEmail /></LazyLoadWrapper>} />
                                                      <Route path="/auth/callback" element={<LazyLoadWrapper><AuthCallback /></LazyLoadWrapper>} />
                                                      <Route path="/simplified-dashboard" element={<Navigate to="/dashboard" replace />} />
                                                      <Route path="/security-insights" element={<LazyLoadWrapper><PageSecurityInsights /></LazyLoadWrapper>} />
                                                      <Route path="/enhanced-security-insights" element={<LazyLoadWrapper><EnhancedSecurityInsights /></LazyLoadWrapper>} />
                                                      <Route path="/blog" element={<LazyLoadWrapper><Blog /></LazyLoadWrapper>} />
                                                      <Route path="/challenges" element={<LazyLoadWrapper><StaticChallenges /></LazyLoadWrapper>} />
                                                      <Route path="/challenges/:challengeId" element={<LazyLoadWrapper><ChallengeSimulator /></LazyLoadWrapper>} />
                                                      <Route path="/games" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><StartHack /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/start-hack" element={
                                                        <Navigate to="/dashboard/start-hack" replace />
                                                      } />
                                                      <Route path="/start-hack/:simulationId" element={
                                                        <Navigate to={`/dashboard/start-hack/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/challenger" element={
                                                        <Navigate to="/dashboard/challenger" replace />
                                                      } />
                                                      <Route path="/challenger/:challengeId" element={
                                                        <Navigate to={`/dashboard/challenger/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/leaderboard" element={<LazyLoadWrapper><SimpleLeaderboard /></LazyLoadWrapper>} />
                                                      <Route path="/learn" element={<LazyLoadWrapper><StaticLearning /></LazyLoadWrapper>} />
                                                      <Route path="/learn/preview/:moduleSlug" element={<LazyLoadWrapper><ModulePreview /></LazyLoadWrapper>} />
                                                      <Route path="/learn/:moduleId" element={<LazyLoadWrapper><LearningModulePage /></LazyLoadWrapper>} />

                                                      {/* Redirect Learning Paths to Dashboard */}
                                                      <Route path="/learning-paths" element={<Navigate to="/dashboard/learning-paths" replace />} />
                                                      <Route path="/learning-paths/:pathId" element={
                                                        <Navigate to={`/dashboard/learning-paths/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/modules/:moduleId" element={
                                                        <Navigate to={`/dashboard/modules/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/learning-paths/:pathId/modules/:moduleId" element={
                                                        <Navigate to={`/dashboard/learning-paths/${window.location.pathname.split('/')[2]}/modules/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/learning-paths/:pathId/module/:moduleId" element={
                                                        <Navigate to={`/dashboard/learning-paths/${window.location.pathname.split('/')[2]}/modules/${window.location.pathname.split('/').pop()}`} replace />
                                                      } />
                                                      <Route path="/products" element={<LazyLoadWrapper><Products /></LazyLoadWrapper>} />
                                                      <Route path="/premium/programs" element={<LazyLoadWrapper><PremiumPrograms /></LazyLoadWrapper>} />
                                                      <Route path="/premium/learn-more" element={<LazyLoadWrapper><LearnMorePage /></LazyLoadWrapper>} />
                                                      <Route path="/premium/features" element={<LazyLoadWrapper><FeaturesPage /></LazyLoadWrapper>} />
                                                      <Route path="/pricing" element={<LazyLoadWrapper><SimplePricing /></LazyLoadWrapper>} />
                                                      <Route path="/contact" element={<LazyLoadWrapper><SimpleContact /></LazyLoadWrapper>} />
                                                      <Route path="/terms" element={<LazyLoadWrapper><TermsAndConditions /></LazyLoadWrapper>} />
                                                      <Route path="/privacy" element={<LazyLoadWrapper><PrivacyPolicy /></LazyLoadWrapper>} />
                                                      <Route path="/about" element={<LazyLoadWrapper><About /></LazyLoadWrapper>} />
                                                      <Route path="/services" element={<LazyLoadWrapper><Services /></LazyLoadWrapper>} />
                                                      <Route path="/simulations" element={<LazyLoadWrapper><Simulations /></LazyLoadWrapper>} />
                                                      <Route path="/simulations/offensive" element={<LazyLoadWrapper><OffensiveSimulations /></LazyLoadWrapper>} />
                                                      <Route path="/simulations/defensive" element={<LazyLoadWrapper><DefensiveSimulations /></LazyLoadWrapper>} />
                                                      <Route path="/test" element={<TestPage />} />
                                                      <Route path="/test-component" element={<Test />} />
                                                      <Route path="/test-login" element={<LazyLoadWrapper><TestLogin /></LazyLoadWrapper>} />
                                                      <Route path="/auth-debug" element={<LazyLoadWrapper><AuthDebug /></LazyLoadWrapper>} />
                                                      <Route path="/database-debug" element={<LazyLoadWrapper><DatabaseDebugComponent /></LazyLoadWrapper>} />
                                                      <Route path="/navbar-test" element={<LazyLoadWrapper><NavbarTest /></LazyLoadWrapper>} />
                                                      <Route path="/subscription-debug" element={<LazyLoadWrapper><SubscriptionDebug /></LazyLoadWrapper>} />

                                                      {/* New global pages with role-based access */}
                                                      <Route path="/global-learn" element={<LazyLoadWrapper><GlobalLearn /></LazyLoadWrapper>} />
                                                      <Route path="/global-challenges" element={<LazyLoadWrapper><GlobalChallenges /></LazyLoadWrapper>} />
                                                      <Route path="/global-start-hack" element={<LazyLoadWrapper><GlobalStartHack /></LazyLoadWrapper>} />

                                                      {/* Dashboard routes with enhanced route handler */}
                                                      <Route path="/dashboard" element={
                                                        <ProtectedRoute>
                                                          <DashboardRouteHandler>
                                                            <DashboardErrorBoundary>
                                                              <LazyLoadWrapper><DashboardLayout /></LazyLoadWrapper>
                                                            </DashboardErrorBoundary>
                                                          </DashboardRouteHandler>
                                                        </ProtectedRoute>
                                                      }>
                                                        {/* Default dashboard home */}
                                                        <Route index element={<LazyLoadWrapper><DashboardHome /></LazyLoadWrapper>} />
                                                        {/* Learning Paths */}
                                                        <Route path="learning-paths" element={<LazyLoadWrapper><LearningPathsSimple /></LazyLoadWrapper>} />
                                                        <Route path="learning-paths/:pathId" element={<LazyLoadWrapper><LearningPathDetail /></LazyLoadWrapper>} />
                                                        <Route path="learning-paths/:pathId/modules/:moduleId" element={<LazyLoadWrapper><ModuleContent /></LazyLoadWrapper>} />

                                                        {/* Start Hack and Challenger */}
                                                        <Route path="start-hack" element={<LazyLoadWrapper><StartHack /></LazyLoadWrapper>} />
                                                        <Route path="start-hack/:simulationId" element={<LazyLoadWrapper><StartHack /></LazyLoadWrapper>} />
                                                        <Route path="challenger" element={<LazyLoadWrapper><Challenger /></LazyLoadWrapper>} />
                                                        <Route path="challenger/:challengeId" element={<LazyLoadWrapper><Challenger /></LazyLoadWrapper>} />

                                                        {/* Dashboard Components */}
                                                        <Route path="challenges" element={<LazyLoadWrapper><ChallengesHub /></LazyLoadWrapper>} />
                                                        <Route path="challenges/:challengeId" element={<LazyLoadWrapper><ChallengeDetail /></LazyLoadWrapper>} />
                                                        <Route path="security-insights" element={<LazyLoadWrapper><SecurityInsights /></LazyLoadWrapper>} />
                                                        <Route path="achievements" element={<LazyLoadWrapper><Achievements /></LazyLoadWrapper>} />
                                                        <Route path="daily-challenges" element={<LazyLoadWrapper><DailyChallenges /></LazyLoadWrapper>} />
                                                        <Route path="leaderboards" element={<LazyLoadWrapper><Leaderboards /></LazyLoadWrapper>} />
                                                        <Route path="certifications" element={<LazyLoadWrapper><Certifications /></LazyLoadWrapper>} />
                                                        <Route path="progress" element={<LazyLoadWrapper><Progress /></LazyLoadWrapper>} />
                                                        <Route path="profile" element={<LazyLoadWrapper><SimpleUserProfile /></LazyLoadWrapper>} />
                                                        <Route path="database-test" element={<LazyLoadWrapper><DatabaseTest /></LazyLoadWrapper>} />
                                                        <Route path="os-content-test" element={<LazyLoadWrapper><OSContentTest /></LazyLoadWrapper>} />
                                                        <Route path="simple-os-test" element={<LazyLoadWrapper><SimpleOSTest /></LazyLoadWrapper>} />
                                                        <Route path="direct-os-test" element={<LazyLoadWrapper><DirectOSTest /></LazyLoadWrapper>} />

                                                        {/* Personalized Learning Features */}
                                                        <Route path="preferences" element={<LazyLoadWrapper><LearningPreferences /></LazyLoadWrapper>} />
                                                        <Route path="skill-assessment/:moduleId" element={<LazyLoadWrapper><SkillAssessment /></LazyLoadWrapper>} />
                                                        <Route path="skill-mapping" element={<LazyLoadWrapper><SkillMapping /></LazyLoadWrapper>} />
                                                        <Route path="time-tracking" element={<LazyLoadWrapper><TimeTrackingDashboard /></LazyLoadWrapper>} />
                                                        <Route path="virtual-lab/:labId" element={<LazyLoadWrapper><VirtualLab /></LazyLoadWrapper>} />
                                                      </Route>


                                                      {/* Redirect old dashboard path to new one */}
                                                      <Route path="/enhanced-dashboard/*" element={<Navigate to="/dashboard" replace />} />

                                                      {/* Gamification Routes */}
                                                      <Route path="/achievements" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AchievementsPage /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/daily-challenges" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><DailyChallengesPage /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/leaderboards" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><LeaderboardPage /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      {/* Redirect simplified dashboard to main dashboard */}
                                                      <Route path="/simplified-dashboard" element={<Navigate to="/dashboard" replace />} />
                                                      <Route path="/learn/modules" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><SimpleLearnModules /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/profile" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><EnhancedProfilePage /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/subscription" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><SubscriptionManagement /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><EnhancedSuperAdminDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin-old" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><ComprehensiveSuperAdminDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AdminGamificationDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification/achievements" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AdminGamificationDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification/daily-challenges" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AdminGamificationDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification/streaks" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AdminGamificationDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification/leaderboards" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><AdminGamificationDashboard /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/gamification/analytics" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><GamificationAnalytics /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />
                                                      <Route path="/admin/database-test" element={
                                                        <ProtectedRoute>
                                                          <LazyLoadWrapper><DatabaseTestPanel /></LazyLoadWrapper>
                                                        </ProtectedRoute>
                                                      } />

                                                      {/* Team routes removed - no business tier */}
                                                    </Routes>

                                                    {/* Show Footer on all routes except dashboards */}
                                                    <Routes>
                                                      <Route path="/dashboard/*" element={null} />
                                                      {/* simplified-dashboard redirects to /dashboard */}
                                                      <Route path="/enhanced-dashboard/*" element={null} />
                                                      <Route path="/admin/*" element={null} />
                                                      {/* Teams routes removed */}
                                                      <Route path="*" element={<GlobalFooterWrapper />} />
                                                    </Routes>

                                                    {/* Scroll to Top Button */}
                                                    <ScrollToTop darkMode={true} />

                                                    {/* Gamification Notifications */}
                                                    <NotificationSystem />

                                                    {/* Toast Notifications */}
                                                    <Toaster
                                                      position="bottom-left"
                                                      toastOptions={{
                                                        duration: 3000,
                                                        style: {
                                                          background: '#333',
                                                          color: '#fff',
                                                          borderRadius: '10px',
                                                        },
                                                      }}
                                                    />

                                                    {/* System Status Indicator */}
                                                    <SystemStatus />
                                                  </div>
                                                </SimpleErrorBoundary>
                                              </div>
                                            </SmoothScroll>
                                          </Router>
                                      </XPProvider>
                                    </GuestProgressProvider>
                                  </AnalyticsProvider>
                                {/* StoreProvider removed */}
                                      </LearningModuleProvider>
                                    </ContextErrorBoundary>
                                  </LearningPreferencesProvider>
                                </LearningProvider>
                              </ContextErrorBoundary>
                            </LeaderboardProvider>
                          </ContextErrorBoundary>
                        </FallbackChallengeProvider>
                      </ContextErrorBoundary>
                    </UnifiedDashboardProvider>
                  </ContextErrorBoundary>
                </SubscriptionProvider>
              </SimpleSubscriptionProvider>
            </ThemeProvider>
          </AuthProvider>
        </LanguageProvider>
      </GlobalThemeProvider>
    </SimpleErrorBoundary>
  );
}

export default App;